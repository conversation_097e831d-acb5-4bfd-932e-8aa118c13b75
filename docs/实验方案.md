### **实验设计方案：真实性验证与谣言传播机制探索**

本实验方案分为两个核心部分：
1.  **系统真实性与有效性验证**：证明您的系统能够复现已知的、符合社会科学理论的宏观和微观现象。这是建立对您模拟结果信任的基础。
2.  **谣言传播机制的探索与展示**：利用您已经过验证的模拟器作为"数字实验室"，深入探究谣言传播的关键机制，并得出有价值的结论。



基于系统字段和工作流程，我为您设计了一套由浅入深、层层递进的实验论证方案。这套方案旨在：
1.  **验证核心机制**：证明您设计的每一个核心模块（如信念网络、人格模型）都按预期工作。
2.  **模拟真实场景**：将系统置于真实世界的复杂场景中（如谣言传播），检验其整体表现。
3.  **展示系统优势**：凸显您的系统相对于传统数值模拟方法的优越性（如可解释性、人格化）。

---

## 实验论证设计方案

### **实验部分一：系统验证 (Micro-level Validation)**

这部分将整个系统作为一个整体，在模拟的知识社区中运行，以探究其宏观动力学。

#### **1.1 经典谣言传播SIR模型复现**

**目标**：模拟一个典型的谣言（低可信度来源，高情感冲击）和一个事实（高可信度来源，中性情感）在社区中的传播曲线，验证系统能否复现SIR（Susceptible-Infected-Recovered）等经典传播模型的基本模式。

*   **实验设计**：
    1.  **社区初始化**：生成一个由50个智能体组成的社区，其人格、认知特质随机分布。
    2.  **谣言注入**：在 第1轮，由一个低影响力的智能体（粉丝数量少）发布一个帖子，其内容是谣言。这个帖子特点是 `source.credibility_score` 低，但 `emotional_impact` 强（如恐惧、愤怒）。
    3.  **事实注入**：在 第5轮时刻，由一个高影响力（权威）的智能体（粉丝数量多）发布一个针对该谣言的辟谣帖子。这个帖子的特点是 `source.credibility_score` 高，但 `emotional_impact` 相对中性。
    4.  **持续演化**：让模拟运行20轮，智能体自由互动。

*   **观测与断言 (群体数据)**：
    *   **"感染"曲线**：追踪持有谣言信念的智能体数量随时间的变化。预期会看到一个先增长后下降的曲线。
    *   **"康复"曲线**：追踪接受辟谣的智能体数量。预期在第五轮后开始上升。


#### **1.2 回音室效应 (Echo Chamber) **

**目标**：验证预设的同质化社交网络是否会形成"回音室"效应，即相似观点的智能体通过互相强化，使群体观点更加极端且难以被外部信息改变。

*   **实验设计**：
    1.  **构建两个同质化社群**：
        *   **社群A (保守派)**：25个智能体，预设相似的认知特质（如都倾向于保守观点），并设置他们的`followers`和`followed_users`主要指向群体内部成员。
        *   **社群B (激进派)**：25个智能体，预设相反的认知特质（如都倾向于激进观点），同样设置内部关注网络。
    2.  **争议话题注入**：在t=1时刻，向两个社群同时注入一个争议性话题的初始观点（如"新政策的利弊"）。
    3.  **内部讨论强化**：在t=2-15期间，让两个社群分别进行内部讨论。由于网络结构，智能体主要接收到来自同观点群体的信息。
    4.  **观点测量**：在t=15时刻，测量两个社群内部观点的一致性和极端程度。
    5.  **外部信息冲击**：在t=16时刻，向两个社群同时注入一个客观、平衡的外部信息，试图纠正极端观点。
    6.  **抗性测试**：观察t=16-25期间，两个社群对外部信息的接受程度。

*   **观测与断言**：
    *   **观点极化**：在t=15时，两个社群内部对争议话题的信念应该变得更加一致和极端，偏离初始的中性立场。
    *   **内部一致性**：测量同一社群内智能体对争议话题相关信念的`confidence`和立场方向，应该显示高度一致性（如社群A都倾向支持，社群B都倾向反对）。
    *   **外部抗性**：在t=16后，回音室内的智能体对外部平衡信息表现出更强的抗性，相关信念的`confidence`下降幅度小，立场改变幅度小。
    *   **信息循环**：通过分析`Memory`传播路径，验证支持群体观点的信息在同质化网络内部出现循环强化，而外部纠正信息传播受阻或被忽视。


#### **1.3  极化现象 **

**目标**： 一个极化的社区，其成员的观点会聚集在"极度同意"和"极度反对"两端，而中间派会减少。

*   **实验设计**：
    1.  **提出指标**：
        *   **OEI**：OEI = (社区中 |veracity_score| ≥ 0.5 的智能体数量) / (社区总智能体数量)
    2.  **谣言注入**：向社区注入谣言。

*   **观测与断言**：
    *   **谣言接受度**：OEI在一个接近 1 的值意味着社区几乎没有中间派，所有人都已"选边站队"，是高度极化的标志。


### **1.4 智能体自我演化验证 (Advanced Feature Validation)**


**目标**：验证智能体的核心认知特质是否会因其经历的"认知冲击"而发生改变。

*   **实验设计 (长期追踪)**：
    1.  **选择一个Agent**：选取一个初始 `skepticism_score` (U-S) 较低（例如0.3）的"天真"智能体。
    2.  **反复受骗**：在模拟的前50个时间步中，让该智能体反复遇到并采纳多个最终被证明是错误的谣言。即，让其经历多次"信念被强力证据推翻"的事件，确保 `deltaV` 多次超过 `U-S`。
    3.  **测试其变化**：在 t=51 时，向这个已经"饱经沧桑"的智能体注入一个新的、与之前类似的谣言。

*   **观测与断言**：
    *   **核心特质演化**：在 t=50 时，检查该智能体的 `skepticism_score` (U-S) 是否已经**显著高于**其初始值0.3。
    *   **行为改变**：在 t=51 时，观察它对新谣言的反应。与它在 t=1 时的行为相比，它现在采纳新谣言的速度应该会慢得多，甚至会直接触发 `verification_threshold` (U-V) 启动求证行为。这证明了智能体实现了"学习与成长"。
