虚拟社区SIR模型对比分析报告

生成时间：2025年08月24日 11:27:18
对比数据集：Run_20250815 vs Run_20250823
分析轮次：第1轮至第40轮，共40轮

==================================================
一、总体传播情况对比
==================================================

1. 感染峰值对比：
   - Run_20250815：33.0%（第26轮）
   - Run_20250823：34.0%（第20轮）
   - 差异：1.0%

2. 最终恢复率对比：
   - Run_20250815：21.0%
   - Run_20250823：26.0%
   - 差异：5.0%

3. 易感人群减少对比：
   - Run_20250815：43.0%
   - Run_20250823：45.0%
   - 差异：2.0%

==================================================
二、Exponential模型参数对比
==================================================

1. 传播率函数参数：
   - Run_20250815: λ(t) = 0.334274·exp(-0.100000·t)
   - Run_20250823: λ(t) = 0.328571·exp(-0.088009·t)

   参数差异：
   - λ₀差异：0.005703
   - λ₁差异：0.011991

2. 恢复率函数参数：
   - Run_20250815: μ(t) = 0.021563·exp(-0.008605·t)
   - Run_20250823: μ(t) = 0.031248·exp(-0.008317·t)

   参数差异：
   - μ₀差异：0.009685
   - μ₁差异：0.000288

==================================================
三、对比总结
==================================================

基于40轮的对比分析，两个数据集在SIR传播模式上的主要差异：

1. 传播强度：数据集2传播更强

2. 恢复能力：数据集2恢复能力更强

3. 模型特征：两个数据集都使用exponential变系数模型，体现了传播动力学的时变特征

==================================================
报告结束
==================================================
