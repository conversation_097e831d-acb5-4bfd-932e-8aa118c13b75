#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from scipy.optimize import curve_fit, minimize
from scipy.integrate import odeint
import logging
from datetime import datetime
import argparse

# 设置字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SIRModelFitter:
    """SIR模型拟合器"""

    def __init__(self):
        self.fitted_params = {}
        self.fitted_curves = {}
        self.model_comparison = {}
        
    def sir_model(self, y, t, beta, gamma):
        """
        SIR模型的微分方程组
        
        Args:
            y: [S, I, R] 当前状态
            t: 时间
            beta: 感染率
            gamma: 恢复率
            
        Returns:
            [dS/dt, dI/dt, dR/dt]
        """
        S, I, R = y
        N = S + I + R  # 总人口
        
        dSdt = -beta * S * I / N
        dIdt = beta * S * I / N - gamma * I
        dRdt = gamma * I
        
        return [dSdt, dIdt, dRdt]

    def sir_model_variable_rates(self, y, t, lambda_func, mu_func):
        """
        变系数SIR模型的微分方程组

        Args:
            y: [S, I, R] 当前状态
            t: 时间
            lambda_func: 传播率函数 λ(t)
            mu_func: 恢复率函数 μ(t)

        Returns:
            [dS/dt, dI/dt, dR/dt]
        """
        S, I, R = y

        # 计算当前时间的传播率和恢复率
        lambda_t = lambda_func(t)
        mu_t = mu_func(t)

        dSdt = -lambda_t * I * S
        dIdt = lambda_t * I * S - mu_t * I
        dRdt = mu_t * I

        return [dSdt, dIdt, dRdt]

    def create_rate_functions(self, t_data, rate_type='linear'):
        """
        创建时变的传播率和恢复率函数

        Args:
            t_data: 时间数据
            rate_type: 函数类型 ('linear', 'exponential', 'polynomial')

        Returns:
            lambda_func, mu_func: 传播率和恢复率函数
        """
        if rate_type == 'linear':
            # 线性变化: λ(t) = λ0 + λ1*t, μ(t) = μ0 + μ1*t
            def lambda_func(t, params):
                lambda0, lambda1 = params[:2]
                return max(0.001, lambda0 + lambda1 * t)

            def mu_func(t, params):
                mu0, mu1 = params[2:4]
                return max(0.001, mu0 + mu1 * t)

        elif rate_type == 'exponential':
            # 指数变化: λ(t) = λ0*exp(λ1*t), μ(t) = μ0*exp(μ1*t)
            def lambda_func(t, params):
                lambda0, lambda1 = params[:2]
                return max(0.001, lambda0 * np.exp(lambda1 * t))

            def mu_func(t, params):
                mu0, mu1 = params[2:4]
                return max(0.001, mu0 * np.exp(mu1 * t))

        elif rate_type == 'polynomial':
            # 二次多项式: λ(t) = λ0 + λ1*t + λ2*t^2, μ(t) = μ0 + μ1*t + μ2*t^2
            def lambda_func(t, params):
                lambda0, lambda1, lambda2 = params[:3]
                return max(0.001, lambda0 + lambda1 * t + lambda2 * t**2)

            def mu_func(t, params):
                mu0, mu1, mu2 = params[3:6]
                return max(0.001, mu0 + mu1 * t + mu2 * t**2)

        return lambda_func, mu_func
    
    def fit_sir_model(self, t_data, s_data, i_data, r_data, population_name=""):
        """
        拟合SIR模型参数
        
        Args:
            t_data: 时间数据
            s_data: 易感者数据
            i_data: 感染者数据
            r_data: 恢复者数据
            population_name: 群体名称
            
        Returns:
            拟合的参数 (beta, gamma) 和拟合质量指标
        """
        logger.info(f"开始拟合 {population_name} 的SIR模型...")
        
        # 数据预处理
        t_data = np.array(t_data)
        s_data = np.array(s_data)
        i_data = np.array(i_data)
        r_data = np.array(r_data)
        
        # 确保数据归一化
        total = s_data + i_data + r_data
        s_data = s_data / total
        i_data = i_data / total
        r_data = r_data / total
        
        # 初始条件
        y0 = [s_data[0], i_data[0], r_data[0]]
        
        def objective(params):
            """目标函数：最小化拟合误差"""
            beta, gamma = params
            
            # 求解微分方程
            try:
                sol = odeint(self.sir_model, y0, t_data, args=(beta, gamma))
                s_pred, i_pred, r_pred = sol.T
                
                # 计算均方误差
                mse = np.mean((s_data - s_pred)**2 + (i_data - i_pred)**2 + (r_data - r_pred)**2)
                return mse
            except:
                return 1e6  # 如果求解失败，返回大的误差值
        
        # 参数优化
        # 初始猜测：beta在0.1-1.0之间，gamma在0.05-0.5之间
        initial_guesses = [
            [0.3, 0.1],
            [0.5, 0.2],
            [0.8, 0.15],
            [0.2, 0.05],
            [1.0, 0.3]
        ]
        
        best_params = None
        best_error = float('inf')
        
        for guess in initial_guesses:
            try:
                # 使用边界约束
                bounds = [(0.01, 2.0), (0.01, 1.0)]  # beta和gamma的合理范围
                result = minimize(objective, guess, bounds=bounds, method='L-BFGS-B')
                
                if result.success and result.fun < best_error:
                    best_error = result.fun
                    best_params = result.x
            except:
                continue
        
        if best_params is None:
            logger.error(f"拟合 {population_name} 失败")
            return None, None, None
        
        beta_opt, gamma_opt = best_params
        
        # 计算拟合曲线
        sol = odeint(self.sir_model, y0, t_data, args=(beta_opt, gamma_opt))
        s_fit, i_fit, r_fit = sol.T
        
        # 计算拟合质量指标
        r2_s = 1 - np.sum((s_data - s_fit)**2) / np.sum((s_data - np.mean(s_data))**2)
        r2_i = 1 - np.sum((i_data - i_fit)**2) / np.sum((i_data - np.mean(i_data))**2)
        r2_r = 1 - np.sum((r_data - r_fit)**2) / np.sum((r_data - np.mean(r_data))**2)
        
        # 计算基本再生数 R0
        R0 = beta_opt / gamma_opt
        
        fit_results = {
            'beta': beta_opt,
            'gamma': gamma_opt,
            'R0': R0,
            'mse': best_error,
            'r2_susceptible': r2_s,
            'r2_infected': r2_i,
            'r2_recovered': r2_r,
            'fitted_curves': {
                'time': t_data.tolist(),
                'susceptible': s_fit.tolist(),
                'infected': i_fit.tolist(),
                'recovered': r_fit.tolist()
            }
        }
        
        logger.info(f"{population_name} 拟合完成:")
        logger.info(f"  β (感染率) = {beta_opt:.4f}")
        logger.info(f"  γ (恢复率) = {gamma_opt:.4f}")
        logger.info(f"  R₀ (基本再生数) = {R0:.4f}")
        logger.info(f"  MSE = {best_error:.6f}")
        logger.info(f"  R² (S,I,R) = ({r2_s:.4f}, {r2_i:.4f}, {r2_r:.4f})")
        
        return fit_results

    def fit_variable_sir_model(self, t_data, s_data, i_data, r_data, population_name="", rate_type='linear'):
        """
        拟合变系数SIR模型参数

        Args:
            t_data: 时间数据
            s_data: 易感者数据
            i_data: 感染者数据
            r_data: 恢复者数据
            population_name: 群体名称
            rate_type: 变化类型 ('linear', 'exponential', 'polynomial')

        Returns:
            拟合的参数和拟合质量指标
        """
        logger.info(f"开始拟合 {population_name} 的变系数SIR模型 ({rate_type})...")

        # 数据预处理
        t_data = np.array(t_data)
        s_data = np.array(s_data)
        i_data = np.array(i_data)
        r_data = np.array(r_data)

        # 确保数据归一化
        total = s_data + i_data + r_data
        s_data = s_data / total
        i_data = i_data / total
        r_data = r_data / total

        # 初始条件
        y0 = [s_data[0], i_data[0], r_data[0]]

        # 获取率函数
        lambda_func, mu_func = self.create_rate_functions(t_data, rate_type)

        def objective(params):
            """目标函数：最小化拟合误差"""
            try:
                # 创建参数化的率函数
                def lambda_t(t):
                    return lambda_func(t, params)
                def mu_t(t):
                    return mu_func(t, params)

                # 求解微分方程
                sol = odeint(self.sir_model_variable_rates, y0, t_data, args=(lambda_t, mu_t))
                s_pred, i_pred, r_pred = sol.T

                # 计算均方误差
                mse = np.mean((s_data - s_pred)**2 + (i_data - i_pred)**2 + (r_data - r_pred)**2)
                return mse
            except:
                return 1e6  # 如果求解失败，返回大的误差值

        # 根据模型类型设置参数数量和边界
        if rate_type == 'linear':
            # [λ0, λ1, μ0, μ1]
            param_count = 4
            bounds = [(0.001, 2.0), (-0.1, 0.1), (0.001, 1.0), (-0.1, 0.1)]
            initial_guesses = [
                [0.2, 0.0, 0.1, 0.0],
                [0.5, -0.01, 0.2, 0.01],
                [0.1, 0.01, 0.05, -0.001],
                [0.3, 0.005, 0.15, 0.002]
            ]
        elif rate_type == 'exponential':
            # [λ0, λ1, μ0, μ1]
            param_count = 4
            bounds = [(0.001, 2.0), (-0.1, 0.1), (0.001, 1.0), (-0.1, 0.1)]
            initial_guesses = [
                [0.2, 0.0, 0.1, 0.0],
                [0.5, -0.01, 0.2, 0.01],
                [0.1, 0.01, 0.05, -0.001],
                [0.3, 0.005, 0.15, 0.002]
            ]
        elif rate_type == 'polynomial':
            # [λ0, λ1, λ2, μ0, μ1, μ2]
            param_count = 6
            bounds = [(0.001, 2.0), (-0.1, 0.1), (-0.01, 0.01), (0.001, 1.0), (-0.1, 0.1), (-0.01, 0.01)]
            initial_guesses = [
                [0.2, 0.0, 0.0, 0.1, 0.0, 0.0],
                [0.5, -0.01, 0.001, 0.2, 0.01, -0.001],
                [0.1, 0.01, -0.001, 0.05, -0.001, 0.0001],
                [0.3, 0.005, 0.0005, 0.15, 0.002, -0.0002]
            ]

        # 参数优化
        best_params = None
        best_error = float('inf')

        for guess in initial_guesses:
            try:
                result = minimize(objective, guess, bounds=bounds, method='L-BFGS-B')

                if result.success and result.fun < best_error:
                    best_error = result.fun
                    best_params = result.x
            except:
                continue

        if best_params is None:
            logger.error(f"拟合 {population_name} 的变系数模型失败")
            return None

        # 计算拟合曲线
        def lambda_t(t):
            return lambda_func(t, best_params)
        def mu_t(t):
            return mu_func(t, best_params)

        sol = odeint(self.sir_model_variable_rates, y0, t_data, args=(lambda_t, mu_t))
        s_fit, i_fit, r_fit = sol.T

        # 计算拟合质量指标
        r2_s = 1 - np.sum((s_data - s_fit)**2) / np.sum((s_data - np.mean(s_data))**2)
        r2_i = 1 - np.sum((i_data - i_fit)**2) / np.sum((i_data - np.mean(i_data))**2)
        r2_r = 1 - np.sum((r_data - r_fit)**2) / np.sum((r_data - np.mean(r_data))**2)

        # 计算平均传播率和恢复率
        lambda_values = [lambda_t(t) for t in t_data]
        mu_values = [mu_t(t) for t in t_data]
        avg_lambda = np.mean(lambda_values)
        avg_mu = np.mean(mu_values)
        avg_R0 = avg_lambda / avg_mu if avg_mu > 0 else float('inf')

        fit_results = {
            'model_type': f'variable_{rate_type}',
            'parameters': best_params.tolist(),
            'avg_lambda': avg_lambda,
            'avg_mu': avg_mu,
            'avg_R0': avg_R0,
            'lambda_values': lambda_values,
            'mu_values': mu_values,
            'mse': best_error,
            'r2_susceptible': r2_s,
            'r2_infected': r2_i,
            'r2_recovered': r2_r,
            'fitted_curves': {
                'time': t_data.tolist(),
                'susceptible': s_fit.tolist(),
                'infected': i_fit.tolist(),
                'recovered': r_fit.tolist()
            }
        }

        logger.info(f"{population_name} 变系数模型拟合完成:")
        logger.info(f"  平均λ (传播率) = {avg_lambda:.4f}")
        logger.info(f"  平均μ (恢复率) = {avg_mu:.4f}")
        logger.info(f"  平均R₀ = {avg_R0:.4f}")
        logger.info(f"  MSE = {best_error:.6f}")
        logger.info(f"  R² (S,I,R) = ({r2_s:.4f}, {r2_i:.4f}, {r2_r:.4f})")

        return fit_results
    
    def generate_differential_equations(self, fit_results, population_name):
        """
        生成微分方程的数学表达式
        
        Args:
            fit_results: 拟合结果
            population_name: 群体名称
            
        Returns:
            微分方程的字符串表示
        """
        beta = fit_results['beta']
        gamma = fit_results['gamma']
        
        equations = {
            'population': population_name,
            'parameters': {
                'beta': beta,
                'gamma': gamma,
                'R0': fit_results['R0']
            },
            'differential_equations': {
                'dS_dt': f"dS/dt = -β·S·I/N = -{beta:.4f}·S·I/N",
                'dI_dt': f"dI/dt = β·S·I/N - γ·I = {beta:.4f}·S·I/N - {gamma:.4f}·I",
                'dR_dt': f"dR/dt = γ·I = {gamma:.4f}·I"
            },
            'mathematical_form': {
                'dS_dt': f"-{beta:.4f} * S * I / N",
                'dI_dt': f"{beta:.4f} * S * I / N - {gamma:.4f} * I",
                'dR_dt': f"{gamma:.4f} * I"
            }
        }
        
        return equations
    
    def load_sir_data(self, json_file_path):
        """
        从JSON文件加载SIR数据
        
        Args:
            json_file_path: JSON文件路径
            
        Returns:
            SIR数据字典
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功加载数据文件: {json_file_path}")
            return data
        except Exception as e:
            logger.error(f"加载数据文件失败: {e}")
            return None
    
    def fit_total_population(self, sir_data):
        """
        拟合总群体的SIR模型

        Args:
            sir_data: SIR数据

        Returns:
            总群体的拟合结果
        """
        results = {}

        # 时间数据
        t_data = sir_data['rounds']

        # 拟合总群体
        total_data = sir_data['total_population']
        total_fit = self.fit_sir_model(
            t_data,
            total_data['susceptible'],
            total_data['infected'],
            total_data['recovered'],
            "总群体"
        )
        if total_fit:
            results['total'] = total_fit
            results['total']['equations'] = self.generate_differential_equations(total_fit, "总群体")

        return results

    def compare_models(self, sir_data):
        """
        比较常系数和变系数SIR模型的拟合效果（仅总群体）

        Args:
            sir_data: SIR数据

        Returns:
            模型比较结果
        """
        logger.info("开始模型比较分析（仅总群体）...")

        # 时间数据
        t_data = sir_data['rounds']

        comparison_results = {}

        # 仅比较总群体
        pop_key, pop_name, pop_data = 'total', '总群体', sir_data['total_population']

        logger.info(f"\n比较 {pop_name} 的模型...")

        pop_comparison = {}

        # 1. 常系数模型
        constant_fit = self.fit_sir_model(
            t_data,
            pop_data['susceptible'],
            pop_data['infected'],
            pop_data['recovered'],
            f"{pop_name}(常系数)"
        )

        if constant_fit:
            pop_comparison['constant'] = constant_fit
            pop_comparison['constant']['model_type'] = 'constant'

        # 2. 线性变系数模型
        linear_fit = self.fit_variable_sir_model(
            t_data,
            pop_data['susceptible'],
            pop_data['infected'],
            pop_data['recovered'],
            f"{pop_name}(线性变系数)",
            'linear'
        )

        if linear_fit:
            pop_comparison['linear'] = linear_fit

        # 3. 指数变系数模型
        exp_fit = self.fit_variable_sir_model(
            t_data,
            pop_data['susceptible'],
            pop_data['infected'],
            pop_data['recovered'],
            f"{pop_name}(指数变系数)",
            'exponential'
        )

        if exp_fit:
            pop_comparison['exponential'] = exp_fit

        # 4. 多项式变系数模型
        poly_fit = self.fit_variable_sir_model(
            t_data,
            pop_data['susceptible'],
            pop_data['infected'],
            pop_data['recovered'],
            f"{pop_name}(多项式变系数)",
            'polynomial'
        )

        if poly_fit:
            pop_comparison['polynomial'] = poly_fit

        # 5. 比较拟合质量
        if pop_comparison:
            best_model = min(pop_comparison.keys(),
                           key=lambda k: pop_comparison[k]['mse'])
            pop_comparison['best_model'] = best_model
            pop_comparison['best_mse'] = pop_comparison[best_model]['mse']

            logger.info(f"{pop_name} 最佳模型: {best_model} (MSE: {pop_comparison['best_mse']:.6f})")

        comparison_results[pop_key] = pop_comparison

        return comparison_results

    def plot_fitted_curves(self, sir_data, fit_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        绘制原始数据和拟合曲线的对比图（仅总群体）

        Args:
            sir_data: 原始SIR数据
            fit_results: 拟合结果
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 设置图形样式
        colors = {
            'susceptible': '#2E8B57',  # 海绿色
            'infected': '#DC143C',     # 深红色
            'recovered': '#4169E1'     # 皇家蓝
        }

        t_data = sir_data['rounds']

        # 仅绘制总群体对比图
        pop_key, pop_name, pop_data = 'total', '总群体', sir_data['total_population']

        if pop_key not in fit_results:
            return

        fit_data = fit_results[pop_key]['fitted_curves']

        plt.figure(figsize=(12, 8))

        # 绘制原始数据（散点）
        plt.subplot(2, 2, 1)
        plt.scatter(t_data, pop_data['susceptible'], color=colors['susceptible'],
                   alpha=0.7, s=30, label='原始数据')
        plt.plot(fit_data['time'], fit_data['susceptible'],
                color=colors['susceptible'], linewidth=2, label='拟合曲线')
        plt.xlabel('轮次')
        plt.ylabel('易感者比例')
        plt.title('易感者 (S)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.subplot(2, 2, 2)
        plt.scatter(t_data, pop_data['infected'], color=colors['infected'],
                   alpha=0.7, s=30, label='原始数据')
        plt.plot(fit_data['time'], fit_data['infected'],
                color=colors['infected'], linewidth=2, label='拟合曲线')
        plt.xlabel('轮次')
        plt.ylabel('感染者比例')
        plt.title('感染者 (I)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.subplot(2, 2, 3)
        plt.scatter(t_data, pop_data['recovered'], color=colors['recovered'],
                   alpha=0.7, s=30, label='原始数据')
        plt.plot(fit_data['time'], fit_data['recovered'],
                color=colors['recovered'], linewidth=2, label='拟合曲线')
        plt.xlabel('轮次')
        plt.ylabel('恢复者比例')
        plt.title('恢复者 (R)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 综合对比图
        plt.subplot(2, 2, 4)
        # 原始数据
        plt.scatter(t_data, pop_data['susceptible'], color=colors['susceptible'],
                   alpha=0.7, s=20, label='S (原始)')
        plt.scatter(t_data, pop_data['infected'], color=colors['infected'],
                   alpha=0.7, s=20, label='I (原始)')
        plt.scatter(t_data, pop_data['recovered'], color=colors['recovered'],
                   alpha=0.7, s=20, label='R (原始)')
        # 拟合曲线
        plt.plot(fit_data['time'], fit_data['susceptible'],
                color=colors['susceptible'], linewidth=2, linestyle='--', label='S (拟合)')
        plt.plot(fit_data['time'], fit_data['infected'],
                color=colors['infected'], linewidth=2, linestyle='--', label='I (拟合)')
        plt.plot(fit_data['time'], fit_data['recovered'],
                color=colors['recovered'], linewidth=2, linestyle='--', label='R (拟合)')
        plt.xlabel('轮次')
        plt.ylabel('比例')
        plt.title('SIR模型拟合对比')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.suptitle(f'{pop_name} SIR模型拟合结果', fontsize=16)
        plt.tight_layout()

        # 保存图片
        output_path = os.path.join(output_dir, f'{pop_key}_sir_fitting.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"{pop_name} 拟合对比图已保存到: {output_path}")

    def plot_model_comparison(self, sir_data, comparison_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        绘制模型比较图（仅总群体）

        Args:
            sir_data: 原始SIR数据
            comparison_results: 模型比较结果
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 设置图形样式
        colors = {
            'susceptible': '#2E8B57',  # 海绿色
            'infected': '#DC143C',     # 深红色
            'recovered': '#4169E1'     # 皇家蓝
        }

        model_colors = {
            'constant': '#FF6B6B',      # 红色
            'linear': '#4ECDC4',        # 青色
            'exponential': '#45B7D1',   # 蓝色
            'polynomial': '#96CEB4'     # 绿色
        }

        t_data = sir_data['rounds']

        # 仅绘制总群体模型比较图
        pop_key, pop_name, pop_data = 'total', '总群体', sir_data['total_population']

        if pop_key not in comparison_results:
            return

        pop_results = comparison_results[pop_key]

        # 创建大图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 绘制S、I、R的比较
        compartments = [
            ('susceptible', 'Susceptible (S)', 0),
            ('infected', 'Infected (I)', 1),
            ('recovered', 'Recovered (R)', 2)
        ]

        for comp_name, comp_title, col_idx in compartments:
            ax = axes[0, col_idx]

            # 绘制原始数据
            ax.scatter(t_data, pop_data[comp_name], color=colors[comp_name],
                      alpha=0.7, s=30, label='Data', zorder=5)

            # 绘制各模型的拟合曲线
            for model_name, model_result in pop_results.items():
                if model_name in ['best_model', 'best_mse']:
                    continue
                if 'fitted_curves' in model_result:
                    fit_data = model_result['fitted_curves']
                    ax.plot(fit_data['time'], fit_data[comp_name],
                           color=model_colors.get(model_name, '#000000'),
                           linewidth=2, label=f'{model_name}',
                           alpha=0.8)

            ax.set_xlabel('Round')
            ax.set_ylabel('Proportion')
            ax.set_title(comp_title)
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 绘制MSE比较
        ax_mse = axes[1, 0]
        model_names = []
        mse_values = []
        for model_name, model_result in pop_results.items():
            if model_name in ['best_model', 'best_mse']:
                continue
            model_names.append(model_name)
            mse_values.append(model_result['mse'])

        bars = ax_mse.bar(model_names, mse_values,
                         color=[model_colors.get(name, '#000000') for name in model_names])
        ax_mse.set_ylabel('MSE')
        ax_mse.set_title('Model Quality Comparison')
        ax_mse.tick_params(axis='x', rotation=45)

        # 标注最佳模型
        if 'best_model' in pop_results:
            best_idx = model_names.index(pop_results['best_model'])
            bars[best_idx].set_color('#FFD700')  # 金色
            ax_mse.text(best_idx, mse_values[best_idx], 'Best',
                       ha='center', va='bottom', fontweight='bold')

        # 绘制R0比较
        ax_r0 = axes[1, 1]
        r0_values = []
        for model_name in model_names:
            model_result = pop_results[model_name]
            if 'R0' in model_result:
                r0_values.append(model_result['R0'])
            elif 'avg_R0' in model_result:
                r0_values.append(model_result['avg_R0'])
            else:
                r0_values.append(0)

        ax_r0.bar(model_names, r0_values,
                 color=[model_colors.get(name, '#000000') for name in model_names])
        ax_r0.set_ylabel('Basic Reproduction Number R₀')
        ax_r0.set_title('R₀ Comparison')
        ax_r0.tick_params(axis='x', rotation=45)
        ax_r0.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='Critical Line')
        ax_r0.legend()

        # 绘制综合拟合效果
        ax_combined = axes[1, 2]
        # 绘制所有模型的I曲线比较
        ax_combined.scatter(t_data, pop_data['infected'], color=colors['infected'],
                          alpha=0.7, s=30, label='Data', zorder=5)

        for model_name, model_result in pop_results.items():
            if model_name in ['best_model', 'best_mse']:
                continue
            if 'fitted_curves' in model_result:
                fit_data = model_result['fitted_curves']
                line_style = '-' if model_name == pop_results.get('best_model') else '--'
                line_width = 3 if model_name == pop_results.get('best_model') else 2
                ax_combined.plot(fit_data['time'], fit_data['infected'],
                               color=model_colors.get(model_name, '#000000'),
                               linewidth=line_width, linestyle=line_style,
                               label=f'{model_name}', alpha=0.8)

        ax_combined.set_xlabel('Round')
        ax_combined.set_ylabel('Infected Proportion')
        ax_combined.set_title('Infected Curve Comparison')
        ax_combined.legend()
        ax_combined.grid(True, alpha=0.3)

        plt.suptitle(f'{pop_name} Model Comparison Analysis', fontsize=16)
        plt.tight_layout()

        # 保存图片
        output_path = os.path.join(output_dir, f'{pop_key}_model_comparison.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"{pop_name} 模型比较图已保存到: {output_path}")

        # 生成总的四图拼接对比图
        self.plot_total_model_comparison(sir_data, comparison_results, output_dir)

    def plot_total_model_comparison(self, sir_data, comparison_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        绘制总的四图拼接对比图（S、I、R拟合 + MSE柱状图）

        Args:
            sir_data: 原始SIR数据
            comparison_results: 模型比较结果
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 设置图形样式
        colors = {
            'susceptible': '#2E8B57',  # 海绿色
            'infected': '#DC143C',     # 深红色
            'recovered': '#4169E1'     # 皇家蓝
        }

        # 柱状图用的莫兰迪色
        model_colors = {
            'constant': '#D4A574',      # 莫兰迪棕
            'linear': '#A8B5A0',        # 莫兰迪绿
            'exponential': '#9EADC8',   # 莫兰迪蓝
            'polynomial': '#C8A8B5'     # 莫兰迪粉
        }

        # 每个子图对应的莫兰迪色系（与数据点颜色相近）
        morandi_colors_by_compartment = {
            'susceptible': {  # 绿色系莫兰迪色
                'constant': '#8FA68E',      # 莫兰迪绿1
                'linear': '#A8B5A0',        # 莫兰迪绿2
                'exponential': '#9CAF88',   # 莫兰迪绿3
                'polynomial': '#B5C4A7'     # 莫兰迪绿4
            },
            'infected': {     # 红色系莫兰迪色
                'constant': '#C8A8A8',      # 莫兰迪红1
                'linear': '#D4A5A5',        # 莫兰迪红2
                'exponential': '#B89B9B',   # 莫兰迪红3
                'polynomial': '#E0B7B7'     # 莫兰迪红4
            },
            'recovered': {    # 蓝色系莫兰迪色（更接近原蓝色）
                'constant': '#7B8FC7',      # 莫兰迪蓝1
                'linear': '#8A9BD1',        # 莫兰迪蓝2
                'exponential': '#6B7FC4',   # 莫兰迪蓝3
                'polynomial': '#9BADD8'     # 莫兰迪蓝4
            }
        }

        # 线型样式
        line_styles = {
            'constant': '--',           # 虚线
            'linear': ':',              # 点线
            'exponential': '-',         # 实线
            'polynomial': '-.'          # 点划线
        }

        t_data = sir_data['rounds']
        pop_key, pop_name, pop_data = 'total', 'Total Population', sir_data['total_population']

        if pop_key not in comparison_results:
            return

        pop_results = comparison_results[pop_key]

        # 创建1x4的横向子图布局，使用gridspec控制间距，调整高度为原来的0.8
        fig = plt.figure(figsize=(16, 3.2))
        gs = fig.add_gridspec(1, 4, width_ratios=[1, 1, 1, 1.2], wspace=0.15, hspace=0)
        axes = [fig.add_subplot(gs[0, i]) for i in range(4)]

        # 绘制S、I、R的拟合对比（前三个子图）
        compartments = [
            ('susceptible', 'Susceptible (S)', 0),
            ('infected', 'Infected (I)', 1),
            ('recovered', 'Recovered (R)', 2)
        ]

        for comp_name, comp_title, idx in compartments:
            ax = axes[idx]

            # 绘制原始数据
            ax.scatter(t_data, pop_data[comp_name], color=colors[comp_name],
                      alpha=0.7, s=15, label='Data', zorder=5)

            # 绘制各模型的拟合曲线
            for model_name, model_result in pop_results.items():
                if model_name in ['best_model', 'best_mse']:
                    continue
                if 'fitted_curves' in model_result:
                    fit_data = model_result['fitted_curves']
                    line_width = 2.5 if model_name == pop_results.get('best_model') else 2.0
                    # 根据compartment选择对应的莫兰迪色
                    morandi_color = morandi_colors_by_compartment[comp_name].get(model_name, '#000000')
                    ax.plot(fit_data['time'], fit_data[comp_name],
                           color=morandi_color,
                           linewidth=line_width,
                           linestyle=line_styles.get(model_name, '-'),
                           label=f'{model_name}', alpha=0.9)

            # 只在第一个子图显示y轴标签
            if idx == 0:
                ax.set_ylabel('Proportion', fontsize=11)

            # 只在中间子图显示x轴标签
            if idx == 1:
                ax.set_xlabel('Period', fontsize=11)

            ax.set_title(comp_title, fontsize=12)

            # 设置x轴刻度和标签 - 假设总共40轮，1/4=10轮，1/2=20轮，3/4=30轮，1=40轮
            ax.set_xticks([10, 20, 30, 40])
            ax.set_xticklabels(['1/4', '1/2', '3/4', '1'])

            ax.grid(True, alpha=0.3)
            ax.tick_params(labelsize=9)

        # 在横轴标题下方添加横向图例，包含曲线和柱状图图例
        handles, labels = axes[0].get_legend_handles_labels()
        # 创建黑色的图例句柄
        black_handles = []
        all_labels = []

        # 添加曲线图例
        for handle, label in zip(handles, labels):
            if label == 'Data':
                # Data用圆点表示
                black_handle = Line2D([0], [0], marker='o', color='black', linestyle='None',
                                    markersize=6, markerfacecolor='black')
            else:
                # 模型用线条表示，使用黑色和对应的线型
                model_name = label
                linestyle = line_styles.get(model_name, '-')
                black_handle = Line2D([0], [0], color='black', linestyle=linestyle, linewidth=2)
            black_handles.append(black_handle)
            all_labels.append(label)

        # 添加柱状图图例
        # MSE (浅色柱子)
        mse_handle = plt.Rectangle((0, 0), 1, 1, facecolor='#D3D3D3', alpha=0.9)
        black_handles.append(mse_handle)
        all_labels.append('MSE')

        # R² (深色柱子)
        r2_handle = plt.Rectangle((0, 0), 1, 1, facecolor='#696969', alpha=0.9)
        black_handles.append(r2_handle)
        all_labels.append('R²')

        fig.legend(black_handles, all_labels, loc='lower center', bbox_to_anchor=(0.5, -0.15),
                  ncol=len(all_labels), frameon=False, fontsize=11)

        # 绘制MSE和R²比较柱状图（第四个子图）
        ax_mse = axes[3]
        model_names = []
        mse_values = []
        r2_values = []
        for model_name, model_result in pop_results.items():
            if model_name in ['best_model', 'best_mse']:
                continue
            model_names.append(model_name)
            mse_values.append(model_result['mse'])
            # 计算平均R²值
            avg_r2 = (model_result['r2_susceptible'] + model_result['r2_infected'] + model_result['r2_recovered']) / 3
            r2_values.append(avg_r2)

        # 创建双y轴
        ax_r2 = ax_mse.twinx()

        # 设置柱状图位置
        x_pos = np.arange(len(model_names))
        width = 0.35

        # 莫兰迪色系（浅色用于MSE）
        morandi_light_colors = ['#E8D5C4', '#D4E8D0', '#D0E1E8', '#E8D0E1']  # 浅莫兰迪色
        morandi_dark_colors = ['#C8A8A8', '#A8C8A8', '#A8A8C8', '#C8A8C8']   # 深莫兰迪色

        # 绘制MSE柱状图（左侧，浅莫兰迪色）
        bars1 = ax_mse.bar(x_pos - width/2, mse_values, width,
                          color=morandi_light_colors[:len(model_names)], alpha=0.9, label='MSE')

        # 绘制R²柱状图（右侧，深莫兰迪色）
        bars2 = ax_r2.bar(x_pos + width/2, r2_values, width,
                         color=morandi_dark_colors[:len(model_names)], alpha=0.9, label='R²')

        # 设置标题（不设置y轴标签）
        ax_mse.set_title('MSE & R²', fontsize=12)

        # 设置x轴
        ax_mse.set_xticks(x_pos)
        ax_mse.set_xticklabels(model_names, rotation=45, fontsize=9)
        ax_mse.tick_params(axis='y', labelsize=9)
        ax_r2.tick_params(axis='y', labelsize=9)

        # 添加虚线网格
        ax_mse.grid(True, alpha=0.3, linestyle='--')

        # 设置科学计数法显示（仅MSE）
        ax_mse.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
        ax_mse.yaxis.get_offset_text().set_fontsize(9)

        # 不再添加最优标记

        # 调整布局，为图例留出更多空间
        plt.tight_layout(rect=[0, 0.15, 1, 1])

        # 保存PNG图片
        png_output_path = os.path.join(output_dir, 'total_model_comparison.png')
        plt.savefig(png_output_path, dpi=300, bbox_inches='tight')

        # 保存PDF图片
        pdf_output_path = os.path.join(output_dir, 'total_model_comparison.pdf')
        plt.savefig(pdf_output_path, format='pdf', bbox_inches='tight')

        plt.close()

        logger.info(f"总的四图拼接对比图已保存到: {png_output_path}")
        logger.info(f"总的四图拼接对比图PDF版本已保存到: {pdf_output_path}")

    def save_fitting_results(self, fit_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        保存拟合结果到JSON文件

        Args:
            fit_results: 拟合结果
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 构建输出数据
        output_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "description": "SIR model fitting results for virtual community experiment",
                "model_description": "SIR (Susceptible-Infected-Recovered) epidemic model",
                "fitting_method": "Least squares optimization with scipy.optimize.minimize"
            },
            "fitting_results": fit_results
        }

        # 保存详细结果
        json_output_path = os.path.join(output_dir, 'sir_fitting_results.json')
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        logger.info(f"拟合结果已保存到: {json_output_path}")

        # 生成可读的报告
        self.generate_report(fit_results, output_dir)

    def generate_report(self, fit_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        生成可读的拟合报告

        Args:
            fit_results: 拟合结果
            output_dir: 输出目录
        """
        report_path = os.path.join(output_dir, 'sir_fitting_report.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("SIR模型拟合报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("模型说明:\n")
            f.write("SIR模型是一个经典的流行病学模型，描述疾病在人群中的传播过程。\n")
            f.write("S(t): 易感者 (Susceptible) - 尚未感染但可能被感染的个体\n")
            f.write("I(t): 感染者 (Infected) - 已感染且具有传染性的个体\n")
            f.write("R(t): 恢复者 (Recovered) - 已恢复且获得免疫的个体\n\n")

            for pop_key, result in fit_results.items():
                pop_name = result['equations']['population']
                f.write("-" * 60 + "\n")
                f.write(f"{pop_name} 拟合结果\n")
                f.write("-" * 60 + "\n")

                # 参数
                f.write("拟合参数:\n")
                f.write(f"  β (感染率): {result['beta']:.6f}\n")
                f.write(f"  γ (恢复率): {result['gamma']:.6f}\n")
                f.write(f"  R₀ (基本再生数): {result['R0']:.6f}\n\n")

                # 拟合质量
                f.write("拟合质量指标:\n")
                f.write(f"  均方误差 (MSE): {result['mse']:.8f}\n")
                f.write(f"  R² (易感者): {result['r2_susceptible']:.6f}\n")
                f.write(f"  R² (感染者): {result['r2_infected']:.6f}\n")
                f.write(f"  R² (恢复者): {result['r2_recovered']:.6f}\n\n")

                # 微分方程
                f.write("动力学微分方程:\n")
                equations = result['equations']['differential_equations']
                f.write(f"  {equations['dS_dt']}\n")
                f.write(f"  {equations['dI_dt']}\n")
                f.write(f"  {equations['dR_dt']}\n\n")

                # 模型解释
                f.write("模型解释:\n")
                if result['R0'] > 1:
                    f.write(f"  R₀ = {result['R0']:.3f} > 1，表明疫情会扩散\n")
                elif result['R0'] < 1:
                    f.write(f"  R₀ = {result['R0']:.3f} < 1，表明疫情会逐渐消退\n")
                else:
                    f.write(f"  R₀ = {result['R0']:.3f} ≈ 1，表明疫情处于临界状态\n")

                f.write(f"  感染率 β = {result['beta']:.4f} 表示易感者与感染者接触后被感染的概率\n")
                f.write(f"  恢复率 γ = {result['gamma']:.4f} 表示感染者恢复的速率\n")
                f.write(f"  平均感染期 = 1/γ = {1/result['gamma']:.2f} 轮次\n\n")

        logger.info(f"拟合报告已保存到: {report_path}")

    def save_model_comparison_results(self, comparison_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        保存模型比较结果

        Args:
            comparison_results: 模型比较结果
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 为每个群体添加最佳模型的拟合函数表达式
        enhanced_comparison_results = {}
        for pop_key, pop_results in comparison_results.items():
            enhanced_pop_results = pop_results.copy()

            # 如果存在最佳模型，添加其拟合函数表达式
            if 'best_model' in pop_results:
                best_model_name = pop_results['best_model']
                if best_model_name in pop_results:
                    best_model_result = pop_results[best_model_name]
                    # 生成最佳模型的拟合函数表达式
                    best_model_equations = self.generate_fitted_equations(best_model_result, best_model_name)
                    enhanced_pop_results['best_model_equations'] = best_model_equations

                    logger.info(f"为{pop_key}群体添加了最佳模型({best_model_name})的拟合函数表达式")

            enhanced_comparison_results[pop_key] = enhanced_pop_results

        # 保存详细的JSON结果
        output_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "description": "SIR model comparison results for virtual community experiment",
                "models_compared": ["constant", "linear_variable", "exponential_variable", "polynomial_variable"],
                "comparison_method": "Mean Squared Error (MSE) minimization"
            },
            "comparison_results": enhanced_comparison_results
        }

        json_output_path = os.path.join(output_dir, 'sir_model_comparison.json')
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        logger.info(f"模型比较结果已保存到: {json_output_path}")

        # 生成比较报告
        self.generate_comparison_report(enhanced_comparison_results, output_dir)

    def generate_fitted_equations(self, model_result, model_name):
        """
        生成拟合后的具体方程

        Args:
            model_result: 模型拟合结果
            model_name: 模型名称

        Returns:
            拟合方程的字符串表示
        """
        equations = {}

        if model_name == 'constant':
            # 常系数模型
            beta = model_result['beta']
            gamma = model_result['gamma']
            equations = {
                'model_type': '常系数模型',
                'rate_functions': {
                    'transmission_rate': f"β = {beta:.6f}",
                    'recovery_rate': f"γ = {gamma:.6f}"
                },
                'differential_equations': {
                    'dS_dt': f"dS/dt = -{beta:.6f}·S·I/N",
                    'dI_dt': f"dI/dt = {beta:.6f}·S·I/N - {gamma:.6f}·I",
                    'dR_dt': f"dR/dt = {gamma:.6f}·I"
                }
            }
        else:
            # 变系数模型
            params = model_result['parameters']
            model_type = model_result['model_type']

            if 'linear' in model_type:
                # 线性变系数模型: λ(t) = λ₀ + λ₁·t, μ(t) = μ₀ + μ₁·t
                lambda0, lambda1, mu0, mu1 = params
                equations = {
                    'model_type': '线性变系数模型',
                    'rate_functions': {
                        'transmission_rate': f"λ(t) = {lambda0:.6f} + {lambda1:.6f}·t",
                        'recovery_rate': f"μ(t) = {mu0:.6f} + {mu1:.6f}·t"
                    },
                    'differential_equations': {
                        'dS_dt': f"dS/dt = -λ(t)·I·S = -({lambda0:.6f} + {lambda1:.6f}·t)·I·S",
                        'dI_dt': f"dI/dt = λ(t)·I·S - μ(t)·I = ({lambda0:.6f} + {lambda1:.6f}·t)·I·S - ({mu0:.6f} + {mu1:.6f}·t)·I",
                        'dR_dt': f"dR/dt = μ(t)·I = ({mu0:.6f} + {mu1:.6f}·t)·I"
                    }
                }
            elif 'exponential' in model_type:
                # 指数变系数模型: λ(t) = λ₀·exp(λ₁·t), μ(t) = μ₀·exp(μ₁·t)
                lambda0, lambda1, mu0, mu1 = params
                equations = {
                    'model_type': '指数变系数模型',
                    'rate_functions': {
                        'transmission_rate': f"λ(t) = {lambda0:.6f}·exp({lambda1:.6f}·t)",
                        'recovery_rate': f"μ(t) = {mu0:.6f}·exp({mu1:.6f}·t)"
                    },
                    'differential_equations': {
                        'dS_dt': f"dS/dt = -λ(t)·I·S = -{lambda0:.6f}·exp({lambda1:.6f}·t)·I·S",
                        'dI_dt': f"dI/dt = λ(t)·I·S - μ(t)·I = {lambda0:.6f}·exp({lambda1:.6f}·t)·I·S - {mu0:.6f}·exp({mu1:.6f}·t)·I",
                        'dR_dt': f"dR/dt = μ(t)·I = {mu0:.6f}·exp({mu1:.6f}·t)·I"
                    }
                }
            elif 'polynomial' in model_type:
                # 多项式变系数模型: λ(t) = λ₀ + λ₁·t + λ₂·t², μ(t) = μ₀ + μ₁·t + μ₂·t²
                lambda0, lambda1, lambda2, mu0, mu1, mu2 = params
                equations = {
                    'model_type': '多项式变系数模型',
                    'rate_functions': {
                        'transmission_rate': f"λ(t) = {lambda0:.6f} + {lambda1:.6f}·t + {lambda2:.6f}·t²",
                        'recovery_rate': f"μ(t) = {mu0:.6f} + {mu1:.6f}·t + {mu2:.6f}·t²"
                    },
                    'differential_equations': {
                        'dS_dt': f"dS/dt = -λ(t)·I·S = -({lambda0:.6f} + {lambda1:.6f}·t + {lambda2:.6f}·t²)·I·S",
                        'dI_dt': f"dI/dt = λ(t)·I·S - μ(t)·I = ({lambda0:.6f} + {lambda1:.6f}·t + {lambda2:.6f}·t²)·I·S - ({mu0:.6f} + {mu1:.6f}·t + {mu2:.6f}·t²)·I",
                        'dR_dt': f"dR/dt = μ(t)·I = ({mu0:.6f} + {mu1:.6f}·t + {mu2:.6f}·t²)·I"
                    }
                }

        return equations

    def generate_comparison_report(self, comparison_results, output_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
        """
        生成模型比较报告

        Args:
            comparison_results: 模型比较结果
            output_dir: 输出目录
        """
        report_path = os.path.join(output_dir, 'sir_model_comparison_report.txt')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("SIR模型比较分析报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("模型说明:\n")
            f.write("1. 常系数模型: dS/dt = -β·S·I/N, dI/dt = β·S·I/N - γ·I, dR/dt = γ·I\n")
            f.write("2. 线性变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I\n")
            f.write("   其中 λ(t) = λ₀ + λ₁·t, μ(t) = μ₀ + μ₁·t\n")
            f.write("3. 指数变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I\n")
            f.write("   其中 λ(t) = λ₀·exp(λ₁·t), μ(t) = μ₀·exp(μ₁·t)\n")
            f.write("4. 多项式变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I\n")
            f.write("   其中 λ(t) = λ₀ + λ₁·t + λ₂·t², μ(t) = μ₀ + μ₁·t + μ₂·t²\n\n")

            # 总结最佳模型
            f.write("最佳模型总结:\n")
            f.write("-" * 40 + "\n")
            if 'total' in comparison_results and 'best_model' in comparison_results['total']:
                best_model = comparison_results['total']['best_model']
                best_mse = comparison_results['total']['best_mse']
                f.write(f"总群体: {best_model}模型 (MSE: {best_mse:.8f})\n")
            f.write("\n")

            # 详细比较结果
            if 'total' in comparison_results:
                pop_results = comparison_results['total']
                pop_name = '总群体'

                f.write("-" * 60 + "\n")
                f.write(f"{pop_name} 详细比较结果\n")
                f.write("-" * 60 + "\n")

                # 按MSE排序显示模型
                models_by_mse = []
                for model_name, model_result in pop_results.items():
                    if model_name in ['best_model', 'best_mse', 'best_model_equations']:
                        continue
                    models_by_mse.append((model_name, model_result))

                models_by_mse.sort(key=lambda x: x[1]['mse'])

                for rank, (model_name, model_result) in enumerate(models_by_mse, 1):
                    f.write(f"\n{rank}. {model_name}模型:\n")
                    f.write(f"   MSE: {model_result['mse']:.8f}\n")

                    # 生成拟合后的方程
                    equations = self.generate_fitted_equations(model_result, model_name)

                    f.write(f"   模型类型: {equations['model_type']}\n")

                    # 拟合后的率函数
                    f.write(f"   拟合后的率函数:\n")
                    f.write(f"     传播率: {equations['rate_functions']['transmission_rate']}\n")
                    f.write(f"     恢复率: {equations['rate_functions']['recovery_rate']}\n")

                    # 拟合后的微分方程
                    f.write(f"   拟合后的微分方程:\n")
                    f.write(f"     {equations['differential_equations']['dS_dt']}\n")
                    f.write(f"     {equations['differential_equations']['dI_dt']}\n")
                    f.write(f"     {equations['differential_equations']['dR_dt']}\n")

                    if 'beta' in model_result and 'gamma' in model_result:
                        # 常系数模型
                        f.write(f"   R₀: {model_result['R0']:.6f}\n")
                    else:
                        # 变系数模型
                        f.write(f"   平均R₀: {model_result['avg_R0']:.6f}\n")
                        f.write(f"   原始参数: {model_result['parameters']}\n")

                    f.write(f"   R² (S,I,R): ({model_result['r2_susceptible']:.4f}, "
                           f"{model_result['r2_infected']:.4f}, {model_result['r2_recovered']:.4f})\n")

                    if rank == 1:
                        f.write("   ★ 最佳模型\n")

                f.write("\n")

            # 模型选择建议
            f.write("=" * 60 + "\n")
            f.write("模型选择建议\n")
            f.write("=" * 60 + "\n")

            # 获取最佳模型
            if 'total' in comparison_results and 'best_model' in comparison_results['total']:
                best_model = comparison_results['total']['best_model']
                f.write(f"最佳模型: {best_model}模型\n\n")

                f.write("建议:\n")
                if best_model != 'constant':
                    f.write(f"1. {best_model}变系数模型表现最佳，说明传播动力学存在时变特征\n")
                    f.write("2. 变系数模型能更好地捕捉传播过程中的动态变化\n")
                    f.write("3. 建议在实际应用中使用变系数模型进行预测和分析\n")
                else:
                    f.write("1. 常系数模型表现最佳，说明传播率相对稳定\n")
                    f.write("2. 简单的常系数模型已能很好地描述传播过程\n")
                    f.write("3. 可以使用常系数模型进行快速分析和预测\n")

        logger.info(f"模型比较报告已保存到: {report_path}")

def find_latest_sir_data(base_dir="/home/<USER>/workspace/virtualcommunity/experiment/sir"):
    """
    查找最新的SIR数据文件

    Args:
        base_dir: 基础目录

    Returns:
        最新的SIR数据文件路径
    """
    import glob

    # 查找所有run_*目录下的sir_curves_data.json文件
    pattern = os.path.join(base_dir, "run_*/sir_curves_data.json")
    files = glob.glob(pattern)

    if not files:
        logger.error(f"在 {base_dir} 中未找到任何SIR数据文件")
        return None

    # 按修改时间排序，获取最新的文件
    files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_file = files[0]

    logger.info(f"找到最新的SIR数据文件: {latest_file}")
    return latest_file

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='SIR模型拟合和微分方程计算')
    parser.add_argument('data_file', nargs='?', help='SIR数据文件路径（可选，默认使用最新文件）')
    parser.add_argument('--output-dir', '-o', default='/home/<USER>/workspace/virtualcommunity/experiment/sir', help='输出目录（默认为sir目录）')
    parser.add_argument('--simple-fit', '-s', action='store_true',
                       help='仅进行简单的常系数模型拟合（默认进行完整的模型比较分析）')

    args = parser.parse_args()

    logger.info("开始SIR模型拟合...")

    # 1. 确定数据文件
    if args.data_file:
        data_file = args.data_file
        logger.info(f"使用指定的数据文件: {data_file}")
    else:
        data_file = find_latest_sir_data()
        if not data_file:
            logger.error("未找到SIR数据文件，程序退出")
            return 1

    # 检查文件是否存在
    if not os.path.exists(data_file):
        logger.error(f"数据文件不存在: {data_file}")
        return 1

    # 2. 创建拟合器
    fitter = SIRModelFitter()

    # 3. 加载数据
    sir_data = fitter.load_sir_data(data_file)
    if not sir_data:
        logger.error("加载SIR数据失败，程序退出")
        return 1

    # 4. 创建输出目录（基于数据文件的目录）
    data_dir = os.path.dirname(os.path.abspath(data_file))
    if args.output_dir == '/home/<USER>/workspace/virtualcommunity/experiment/sir':
        output_dir = data_dir
    else:
        output_dir = args.output_dir

    if args.simple_fit:
        # 简单拟合模式（仅常系数模型）
        logger.info("运行简单SIR模型拟合（仅常系数模型）...")

        # 5. 拟合总群体的模型
        fit_results = fitter.fit_total_population(sir_data)
        if not fit_results:
            logger.error("拟合失败，程序退出")
            return 1

        # 6. 绘制拟合对比图
        fitter.plot_fitted_curves(sir_data, fit_results, output_dir)

        # 7. 保存拟合结果
        fitter.save_fitting_results(fit_results, output_dir)

        # 8. 输出简要结果到控制台
        print("\n" + "="*80)
        print("SIR模型拟合结果摘要")
        print("="*80)

        for _, result in fit_results.items():
            pop_name = result['equations']['population']
            print(f"\n{pop_name}:")
            print(f"  β (感染率): {result['beta']:.6f}")
            print(f"  γ (恢复率): {result['gamma']:.6f}")
            print(f"  R₀ (基本再生数): {result['R0']:.6f}")
            print(f"  拟合质量 (MSE): {result['mse']:.8f}")

            print("\n  微分方程:")
            equations = result['equations']['differential_equations']
            print(f"    {equations['dS_dt']}")
            print(f"    {equations['dI_dt']}")
            print(f"    {equations['dR_dt']}")

        print(f"\n详细结果已保存到: {output_dir}")
        logger.info("SIR模型拟合完成！")

    else:
        # 默认模式：完整的模型比较分析
        logger.info("运行完整的模型比较分析（常系数 + 变系数模型）...")

        # 5. 比较不同模型
        comparison_results = fitter.compare_models(sir_data)
        if not comparison_results:
            logger.error("模型比较失败，程序退出")
            return 1

        # 6. 绘制模型比较图
        fitter.plot_model_comparison(sir_data, comparison_results, output_dir)

        # 7. 保存比较结果
        fitter.save_model_comparison_results(comparison_results, output_dir)

        # 8. 输出比较结果摘要
        print("\n" + "="*80)
        print("SIR模型比较分析结果摘要（总群体）")
        print("="*80)

        if 'total' in comparison_results:
            pop_results = comparison_results['total']

            if 'best_model' in pop_results:
                best_model = pop_results['best_model']
                best_mse = pop_results['best_mse']
                print("\n总群体:")
                print(f"  最佳模型: {best_model}")
                print(f"  最佳MSE: {best_mse:.8f}")

                # 显示所有模型的MSE
                print("  所有模型MSE比较:")
                models_by_mse = []
                for model_name, model_result in pop_results.items():
                    if model_name in ['best_model', 'best_mse']:
                        continue
                    models_by_mse.append((model_name, model_result['mse']))

                models_by_mse.sort(key=lambda x: x[1])
                for rank, (model_name, mse) in enumerate(models_by_mse, 1):
                    marker = "★" if model_name == best_model else " "
                    print(f"    {rank}. {model_name}: {mse:.8f} {marker}")

        print(f"\n详细比较结果已保存到: {output_dir}")
        logger.info("SIR模型比较分析完成！")

    return 0

if __name__ == "__main__":
    sys.exit(main())
