import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 设置matplotlib为非交互式后端
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def load_best_model_data():
    """加载最佳模型的数据"""
    # 查找最新的运行目录
    sir_dir = Path('.')
    run_dirs = [d for d in sir_dir.iterdir() if d.is_dir() and d.name.startswith('run_')]
    if not run_dirs:
        raise FileNotFoundError("未找到运行目录")
    
    latest_run = max(run_dirs, key=lambda x: x.name)
    json_file = latest_run / 'sir_model_comparison.json'
    
    if not json_file.exists():
        raise FileNotFoundError(f"未找到文件: {json_file}")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data

def plot_rate_functions():
    """绘制最佳模型的传播率和恢复率函数"""
    # 加载数据
    data = load_best_model_data()
    
    # 获取最佳模型的参数
    best_model_info = data['comparison_results']['total']['best_model_equations']
    
    # 从指数模型中提取参数
    exponential_data = data['comparison_results']['total']['exponential']
    params = exponential_data['parameters']
    
    # 参数解释：
    # params[0]: λ0 (传播率初始值)
    # params[1]: α (传播率衰减系数)  
    # params[2]: μ0 (恢复率初始值)
    # params[3]: β (恢复率衰减系数)
    
    lambda_0 = params[0]  # 0.32857103207315547
    alpha = params[1]     # -0.08800859601286361
    mu_0 = params[2]      # 0.031248402362066066
    beta = params[3]      # -0.008317309231221821
    
    # 时间范围
    t = np.linspace(1, 40, 100)
    
    # 计算函数值
    lambda_t = lambda_0 * np.exp(alpha * t)
    mu_t = mu_0 * np.exp(beta * t)
    
    # 创建图形 - 分别绘制
    plt.figure(figsize=(12, 8))

    # 绘制传播率函数
    plt.subplot(2, 1, 1)
    plt.plot(t, lambda_t, 'b-', linewidth=2, label='Transmission Rate λ(t)')
    plt.title('Transmission Rate Function: λ(t) = 0.328571·exp(-0.088009·t)', fontsize=14, fontweight='bold')
    plt.xlabel('Time t (days)', fontsize=12)
    plt.ylabel('Transmission Rate λ(t)', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)

    # 添加函数方程文本
    equation_text = f'λ(t) = {lambda_0:.6f}·exp({alpha:.6f}·t)'
    plt.text(0.05, 0.95, equation_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 绘制恢复率函数
    plt.subplot(2, 1, 2)
    plt.plot(t, mu_t, 'r-', linewidth=2, label='Recovery Rate μ(t)')
    plt.title('Recovery Rate Function: μ(t) = 0.031248·exp(-0.008317·t)', fontsize=14, fontweight='bold')
    plt.xlabel('Time t (days)', fontsize=12)
    plt.ylabel('Recovery Rate μ(t)', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)

    # 添加函数方程文本
    equation_text = f'μ(t) = {mu_0:.6f}·exp({beta:.6f}·t)'
    plt.text(0.05, 0.95, equation_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))

    plt.tight_layout()

    # 保存图片
    output_file = 'best_model_rate_functions.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"图片已保存为: {output_file}")
    plt.close()

    # 创建对比图 - 两条函数在同一个图上
    plt.figure(figsize=(12, 6))

    # 使用双y轴来更好地显示两条函数
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # 绘制传播率函数
    color = 'tab:blue'
    ax1.set_xlabel('Time t (days)', fontsize=12)
    ax1.set_ylabel('Transmission Rate λ(t)', color=color, fontsize=12)
    line1 = ax1.plot(t, lambda_t, color=color, linewidth=2, label='Transmission Rate λ(t)')
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)

    # 创建第二个y轴
    ax2 = ax1.twinx()
    color = 'tab:red'
    ax2.set_ylabel('Recovery Rate μ(t)', color=color, fontsize=12)
    line2 = ax2.plot(t, mu_t, color=color, linewidth=2, label='Recovery Rate μ(t)')
    ax2.tick_params(axis='y', labelcolor=color)

    # 添加标题
    plt.title('Best Model Rate Functions Comparison', fontsize=14, fontweight='bold')

    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

    # 添加函数方程文本
    equation_text = f'λ(t) = {lambda_0:.6f}·exp({alpha:.6f}·t)\nμ(t) = {mu_0:.6f}·exp({beta:.6f}·t)'
    ax1.text(0.05, 0.95, equation_text, transform=ax1.transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

    plt.tight_layout()

    # 保存对比图
    comparison_file = 'best_model_rate_functions_comparison.png'
    plt.savefig(comparison_file, dpi=300, bbox_inches='tight')
    print(f"对比图已保存为: {comparison_file}")
    plt.close()
    
    # 打印一些统计信息
    print("\n=== 最佳模型参数信息 ===")
    print(f"模型类型: {best_model_info['model_type']}")
    print(f"传播率函数: {best_model_info['rate_functions']['transmission_rate']}")
    print(f"恢复率函数: {best_model_info['rate_functions']['recovery_rate']}")
    print(f"\n参数值:")
    print(f"λ₀ = {lambda_0:.6f}")
    print(f"α = {alpha:.6f}")
    print(f"μ₀ = {mu_0:.6f}")
    print(f"β = {beta:.6f}")
    
    print(f"\n在t=1时:")
    print(f"λ(1) = {lambda_0 * np.exp(alpha * 1):.6f}")
    print(f"μ(1) = {mu_0 * np.exp(beta * 1):.6f}")
    
    print(f"\n在t=40时:")
    print(f"λ(40) = {lambda_0 * np.exp(alpha * 40):.6f}")
    print(f"μ(40) = {mu_0 * np.exp(beta * 40):.6f}")

if __name__ == "__main__":
    plot_rate_functions()
